@{
    ViewData["Title"] = "Báo cáo và thống kê";
}

<div class="container-fluid py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a asp-controller="Admin" asp-action="Index">Trang chủ</a></li>
                    <li class="breadcrumb-item"><a asp-controller="BookingManagement" asp-action="Index">Quản lý đặt vé</a></li>
                    <li class="breadcrumb-item active" aria-current="page">@ViewData["Title"]</li>
                </ol>
            </nav>
            <h2 class="mb-0"><i class="bi bi-graph-up me-2"></i>@ViewData["Title"]</h2>
        </div>
        <div>
            <a asp-action="Index" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left me-1"></i>Quay lại
            </a>
        </div>
    </div>

    <!-- Thống kê tổng quan -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card admin-card h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="icon-box bg-primary bg-opacity-10 text-primary rounded-circle me-3">
                            <i class="bi bi-bus-front fs-4"></i>
                        </div>
                        <div>
                            <div class="text-muted small fw-medium">Tổng chuyến xe</div>
                            <h3 class="mb-0">@ViewBag.TotalTrips</h3>
                            <div class="text-success small">
                                <i class="bi bi-arrow-up me-1"></i>@ViewBag.MonthlyTrips tháng này
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card admin-card h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="icon-box bg-success bg-opacity-10 text-success rounded-circle me-3">
                            <i class="bi bi-ticket fs-4"></i>
                        </div>
                        <div>
                            <div class="text-muted small fw-medium">Tổng vé đã bán</div>
                            <h3 class="mb-0">@ViewBag.TotalTickets</h3>
                            <div class="text-success small">
                                <i class="bi bi-arrow-up me-1"></i>@ViewBag.MonthlyTickets tháng này
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card admin-card h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="icon-box bg-warning bg-opacity-10 text-warning rounded-circle me-3">
                            <i class="bi bi-cash-coin fs-4"></i>
                        </div>
                        <div>
                            <div class="text-muted small fw-medium">Tổng doanh thu</div>
                            <h3 class="mb-0">@ViewBag.TotalRevenue.ToString("#,##0")₫</h3>
                            <div class="text-success small">
                                <i class="bi bi-arrow-up me-1"></i>@ViewBag.MonthlyRevenue.ToString("#,##0")₫ tháng này
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card admin-card h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="icon-box bg-info bg-opacity-10 text-info rounded-circle me-3">
                            <i class="bi bi-calendar-month fs-4"></i>
                        </div>
                        <div>
                            <div class="text-muted small fw-medium">Tháng hiện tại</div>
                            <h3 class="mb-0">@ViewBag.CurrentMonth</h3>
                            <div class="text-muted small">
                                Báo cáo chi tiết
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Biểu đồ -->
    <div class="row mb-4">
        <div class="col-lg-8">
            <div class="card admin-card">
                <div class="card-header bg-white py-3">
                    <h5 class="mb-0"><i class="bi bi-graph-up me-2"></i>Doanh thu theo ngày (Tháng @ViewBag.CurrentMonth)</h5>
                </div>
                <div class="card-body">
                    <canvas id="dailyRevenueChart" height="100"></canvas>
                </div>
            </div>
        </div>
        <div class="col-lg-4">
            <div class="card admin-card">
                <div class="card-header bg-white py-3">
                    <h5 class="mb-0"><i class="bi bi-pie-chart me-2"></i>Phân bố trạng thái vé</h5>
                </div>
                <div class="card-body">
                    <canvas id="ticketStatusChart" height="150"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Top tuyến đường -->
    <div class="row mb-4">
        <div class="col-lg-6">
            <div class="card admin-card">
                <div class="card-header bg-white py-3 d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="bi bi-trophy me-2"></i>Top tuyến đường theo doanh thu</h5>
                    <button class="btn btn-sm btn-outline-success" onclick="exportRouteRevenue()">
                        <i class="bi bi-file-earmark-excel me-1"></i>Xuất Excel
                    </button>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>#</th>
                                    <th>Tuyến đường</th>
                                    <th>Doanh thu</th>
                                    <th>Số vé</th>
                                    <th>Chuyến xe</th>
                                </tr>
                            </thead>
                            <tbody>
                                @{int index = 1;}
                                @foreach (var route in ViewBag.TopRoutesByRevenue)
                                {
                                    <tr>
                                        <td>
                                            @if (index <= 3)
                                            {
                                                <span class="badge bg-warning">@index</span>
                                            }
                                            else
                                            {
                                                <span class="text-muted">@index</span>
                                            }
                                        </td>
                                        <td class="fw-medium">@route.Route</td>
                                        <td class="text-success fw-bold">@route.Revenue.ToString("#,##0")₫</td>
                                        <td>@route.TicketCount</td>
                                        <td>@route.TripCount</td>
                                    </tr>
                                    index++;
                                }
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-6">
            <div class="card admin-card">
                <div class="card-header bg-white py-3 d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="bi bi-ticket-perforated me-2"></i>Top tuyến đường theo số vé</h5>
                    <button class="btn btn-sm btn-outline-success" onclick="exportRouteTickets()">
                        <i class="bi bi-file-earmark-excel me-1"></i>Xuất Excel
                    </button>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>#</th>
                                    <th>Tuyến đường</th>
                                    <th>Số vé</th>
                                    <th>Doanh thu</th>
                                    <th>Giá TB</th>
                                </tr>
                            </thead>
                            <tbody>
                                @{int ticketIndex = 1;}
                                @foreach (var route in ViewBag.TopRoutesByTickets)
                                {
                                    <tr>
                                        <td>
                                            @if (ticketIndex <= 3)
                                            {
                                                <span class="badge bg-primary">@ticketIndex</span>
                                            }
                                            else
                                            {
                                                <span class="text-muted">@ticketIndex</span>
                                            }
                                        </td>
                                        <td class="fw-medium">@route.Route</td>
                                        <td class="text-primary fw-bold">@route.TicketCount</td>
                                        <td>@route.Revenue.ToString("#,##0")₫</td>
                                        <td>@route.AvgPrice.ToString("#,##0")₫</td>
                                    </tr>
                                    ticketIndex++;
                                }
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Thống kê theo loại xe -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card admin-card">
                <div class="card-header bg-white py-3 d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="bi bi-truck me-2"></i>Thống kê theo loại xe</h5>
                    <button class="btn btn-sm btn-outline-success" onclick="exportVehicleStats()">
                        <i class="bi bi-file-earmark-excel me-1"></i>Xuất Excel
                    </button>
                </div>
                <div class="card-body">
                    <div class="row">
                        @foreach (var vehicle in ViewBag.VehicleTypeStats)
                        {
                            <div class="col-lg-3 col-md-6 mb-3">
                                <div class="card border-0 bg-light h-100">
                                    <div class="card-body text-center">
                                        <h6 class="card-title text-primary">@vehicle.VehicleType</h6>
                                        <div class="row text-center">
                                            <div class="col-6">
                                                <div class="text-muted small">Chuyến xe</div>
                                                <div class="fw-bold">@vehicle.TripCount</div>
                                            </div>
                                            <div class="col-6">
                                                <div class="text-muted small">Số vé</div>
                                                <div class="fw-bold">@vehicle.TicketCount</div>
                                            </div>
                                        </div>
                                        <hr class="my-2">
                                        <div class="row text-center">
                                            <div class="col-6">
                                                <div class="text-muted small">Doanh thu</div>
                                                <div class="fw-bold text-success">@vehicle.Revenue.ToString("#,##0")₫</div>
                                            </div>
                                            <div class="col-6">
                                                <div class="text-muted small">Lấp đầy TB</div>
                                                <div class="fw-bold text-info">@vehicle.AvgOccupancy.ToString("F1")%</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        }
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Nút xuất báo cáo tổng hợp -->
    <div class="row">
        <div class="col-12">
            <div class="card admin-card">
                <div class="card-header bg-white py-3">
                    <h5 class="mb-0"><i class="bi bi-download me-2"></i>Xuất báo cáo tổng hợp</h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-md-3">
                            <button class="btn btn-success w-100" onclick="exportFullReport()">
                                <i class="bi bi-file-earmark-excel me-2"></i>Báo cáo đầy đủ (Excel)
                            </button>
                        </div>
                        <div class="col-md-3">
                            <button class="btn btn-info w-100" onclick="exportRevenueReport()">
                                <i class="bi bi-cash-coin me-2"></i>Báo cáo doanh thu
                            </button>
                        </div>
                        <div class="col-md-3">
                            <button class="btn btn-warning w-100" onclick="exportCustomReport()">
                                <i class="bi bi-gear me-2"></i>Báo cáo tùy chỉnh
                            </button>
                        </div>
                        <div class="col-md-3">
                            <button class="btn btn-secondary w-100" onclick="exportPdfReport()">
                                <i class="bi bi-file-earmark-pdf me-2"></i>Xuất PDF
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        $(document).ready(function() {
            // Highlight menu item
            $('#menu-booking-management').addClass('active');

            // Tạo biểu đồ
            createCharts();
        });

        function createCharts() {
            // Biểu đồ doanh thu theo ngày
            var dailyData = @Html.Raw(Json.Serialize(ViewBag.DailyRevenueThisMonth));
            var dailyLabels = dailyData.map(d => new Date(d.date).getDate());
            var dailyRevenues = dailyData.map(d => d.revenue);

            var ctx1 = document.getElementById('dailyRevenueChart').getContext('2d');
            new Chart(ctx1, {
                type: 'line',
                data: {
                    labels: dailyLabels,
                    datasets: [{
                        label: 'Doanh thu (VNĐ)',
                        data: dailyRevenues,
                        borderColor: '#0d6efd',
                        backgroundColor: 'rgba(13, 110, 253, 0.1)',
                        borderWidth: 2,
                        fill: true,
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return new Intl.NumberFormat('vi-VN').format(value) + '₫';
                                }
                            }
                        },
                        x: {
                            title: {
                                display: true,
                                text: 'Ngày trong tháng'
                            }
                        }
                    },
                    plugins: {
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    return 'Doanh thu: ' + new Intl.NumberFormat('vi-VN').format(context.parsed.y) + '₫';
                                }
                            }
                        }
                    }
                }
            });

            // Biểu đồ trạng thái vé
            var statusData = @Html.Raw(Json.Serialize(ViewBag.TicketStatusStats));
            var statusLabels = statusData.map(s => getStatusText(s.status));
            var statusCounts = statusData.map(s => s.count);

            var ctx2 = document.getElementById('ticketStatusChart').getContext('2d');
            new Chart(ctx2, {
                type: 'doughnut',
                data: {
                    labels: statusLabels,
                    datasets: [{
                        data: statusCounts,
                        backgroundColor: [
                            '#0d6efd',
                            '#198754',
                            '#17a2b8',
                            '#dc3545',
                            '#ffc107',
                            '#6f42c1'
                        ],
                        borderWidth: 2,
                        borderColor: '#fff'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                padding: 15,
                                usePointStyle: true
                            }
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    var total = context.dataset.data.reduce((a, b) => a + b, 0);
                                    var percentage = ((context.parsed * 100) / total).toFixed(1);
                                    return context.label + ': ' + context.parsed + ' vé (' + percentage + '%)';
                                }
                            }
                        }
                    }
                }
            });
        }

        function getStatusText(status) {
            switch(status) {
                case 0: return 'Đã đặt';
                case 1: return 'Đã thanh toán';
                case 2: return 'Đã sử dụng';
                case 3: return 'Đã hoàn thành';
                case 4: return 'Đã hủy';
                case 5: return 'Đã hoàn tiền';
                default: return 'Không xác định';
            }
        }

        // Các hàm xuất báo cáo
        function exportRouteRevenue() {
            window.open('@Url.Action("ExportRouteReport", "BookingManagement")?type=revenue', '_blank');
        }

        function exportRouteTickets() {
            window.open('@Url.Action("ExportRouteReport", "BookingManagement")?type=tickets', '_blank');
        }

        function exportVehicleStats() {
            window.open('@Url.Action("ExportVehicleReport", "BookingManagement")', '_blank');
        }

        function exportFullReport() {
            window.open('@Url.Action("ExportFullReport", "BookingManagement")', '_blank');
        }

        function exportRevenueReport() {
            window.open('@Url.Action("ExportRevenueReport", "BookingManagement")', '_blank');
        }

        function exportCustomReport() {
            // Hiển thị modal để chọn tùy chỉnh
            $('#customReportModal').modal('show');
        }

        function exportPdfReport() {
            window.open('@Url.Action("ExportPdfReport", "BookingManagement")', '_blank');
        }
    </script>
}

@section Styles {
    <style>
        .admin-card {
            border: none;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            transition: all 0.3s ease;
        }

        .admin-card:hover {
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
            transform: translateY(-2px);
        }

        .icon-box {
            width: 3rem;
            height: 3rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .table th {
            font-weight: 600;
            border-bottom: 2px solid #dee2e6;
        }

        .badge {
            font-size: 0.75rem;
        }

        canvas {
            max-height: 400px;
        }

        @media (max-width: 768px) {
            canvas {
                max-height: 300px;
            }
        }
    </style>
}
