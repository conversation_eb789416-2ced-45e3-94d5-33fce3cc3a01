@model List<DatVeXe.Models.ChuyenXe>
@{
    ViewData["Title"] = "Quản lý đơn đặt vé";
    var today = DateTime.Now.Date;
    var endDate = today.AddDays(7);
    var ngayKhoiHanh = ViewBag.NgayKhoiHanh;
    var trangThai = ViewBag.TrangThai ?? "all";
}

<div class="container-fluid py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a asp-controller="Admin" asp-action="Index">Trang chủ</a></li>
                    <li class="breadcrumb-item active" aria-current="page">@ViewData["Title"]</li>
                </ol>
            </nav>
            <h2 class="mb-0"><i class="bi bi-ticket-detailed me-2"></i>@ViewData["Title"]</h2>
        </div>
        <div>
            <a asp-action="Reports" class="btn btn-outline-info me-2">
                <i class="bi bi-graph-up me-1"></i>Báo cáo & Thống kê
            </a>
            <a asp-controller="Admin" asp-action="Index" class="btn btn-outline-secondary">
                <i class="bi bi-house me-1"></i>Trang chủ
            </a>
        </div>
    </div>

    <!-- Thống kê nhanh và bộ lọc -->
    <div class="row mb-4 g-3 align-items-stretch">
        <div class="col-lg-8">
            <div class="row g-3">
                <!-- Dashboard Cards -->
                <div class="col-md-3">
                    <div class="card admin-card h-100">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="icon-box bg-primary bg-opacity-10 text-primary rounded-circle me-3">
                                    <i class="bi bi-calendar-check fs-4"></i>
                                </div>
                                <div>
                                    <div class="text-muted small fw-medium">Tổng chuyến xe</div>
                                    <h3 class="mb-0">@Model.Count</h3>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card admin-card h-100">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="icon-box bg-success bg-opacity-10 text-success rounded-circle me-3">
                                    <i class="bi bi-ticket fs-4"></i>
                                </div>
                                <div>
                                    <div class="text-muted small fw-medium">Tổng vé đã đặt</div>
                                    <h3 class="mb-0">@Model.Sum(c => c.Ves?.Count ?? 0)</h3>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card admin-card h-100">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="icon-box bg-info bg-opacity-10 text-info rounded-circle me-3">
                                    <i class="bi bi-check-circle fs-4"></i>
                                </div>
                                <div>
                                    <div class="text-muted small fw-medium">Vé đã hoàn thành</div>
                                    <h3 class="mb-0">@Model.Sum(c => c.Ves?.Count(v => v.TrangThai == TrangThaiVe.DaHoanThanh) ?? 0)</h3>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card admin-card h-100">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="icon-box bg-danger bg-opacity-10 text-danger rounded-circle me-3">
                                    <i class="bi bi-x-circle fs-4"></i>
                                </div>
                                <div>
                                    <div class="text-muted small fw-medium">Vé đã hủy</div>
                                    <h3 class="mb-0">@Model.Sum(c => c.Ves?.Count(v => v.TrangThai == TrangThaiVe.DaHuy) ?? 0)</h3>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-4">
            <form method="get" asp-action="Index" class="card admin-card h-100">
                <div class="card-header bg-white py-3 d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="bi bi-funnel me-2"></i>Bộ lọc nâng cao</h5>
                    <button type="button" class="btn btn-sm btn-outline-primary" id="toggleAdvancedFilter">
                        <i class="bi bi-chevron-down me-1"></i>Mở rộng
                    </button>
                </div>
                <div class="card-body">
                    <!-- Bộ lọc cơ bản -->
                    <div class="row g-3">
                        <div class="col-12">
                            <label class="form-label">Ngày khởi hành</label>
                            <input type="date" name="ngayKhoiHanh" class="form-control" value="@(ngayKhoiHanh?.ToString("yyyy-MM-dd") ?? "")" />
                        </div>
                        <div class="col-12">
                            <label class="form-label">Trạng thái</label>
                            <select name="trangThai" class="form-select">
                                <option value="all" selected="@(trangThai == "all")">Tất cả</option>
                                <option value="chua_khoi_hanh" selected="@(trangThai == "chua_khoi_hanh")">Sắp khởi hành</option>
                                <option value="da_khoi_hanh" selected="@(trangThai == "da_khoi_hanh")">Đã khởi hành</option>
                            </select>
                        </div>
                        <div class="col-12">
                            <label class="form-label">Tìm kiếm</label>
                            <input type="text" name="searchKeyword" class="form-control"
                                   placeholder="Tìm theo tuyến đường, biển số xe..." value="@ViewBag.SearchKeyword" />
                        </div>
                    </div>

                    <!-- Bộ lọc nâng cao (ẩn mặc định) -->
                    <div id="advancedFilters" class="row g-3 mt-2" style="display: none;">
                        <div class="col-12">
                            <hr class="my-2">
                            <small class="text-muted fw-medium">BỘ LỌC NÂNG CAO</small>
                        </div>
                        <div class="col-6">
                            <label class="form-label">Từ ngày</label>
                            <input type="date" name="tuNgay" class="form-control" value="@ViewBag.TuNgay" />
                        </div>
                        <div class="col-6">
                            <label class="form-label">Đến ngày</label>
                            <input type="date" name="denNgay" class="form-control" value="@ViewBag.DenNgay" />
                        </div>
                        <div class="col-12">
                            <label class="form-label">Loại xe</label>
                            <select name="loaiXe" class="form-select">
                                <option value="">Tất cả loại xe</option>
                                <option value="Giường nằm" @(ViewBag.LoaiXe == "Giường nằm" ? "selected" : "")>Giường nằm</option>
                                <option value="Ghế ngồi" @(ViewBag.LoaiXe == "Ghế ngồi" ? "selected" : "")>Ghế ngồi</option>
                                <option value="Limousine" @(ViewBag.LoaiXe == "Limousine" ? "selected" : "")>Limousine</option>
                            </select>
                        </div>
                        <div class="col-6">
                            <label class="form-label">Giá từ</label>
                            <input type="number" name="minGia" class="form-control"
                                   placeholder="0" value="@ViewBag.MinGia" min="0" step="1000" />
                        </div>
                        <div class="col-6">
                            <label class="form-label">Giá đến</label>
                            <input type="number" name="maxGia" class="form-control"
                                   placeholder="1000000" value="@ViewBag.MaxGia" min="0" step="1000" />
                        </div>
                        <div class="col-12">
                            <label class="form-label">Tình trạng ghế</label>
                            <select name="tinhTrangGhe" class="form-select">
                                <option value="">Tất cả</option>
                                <option value="con_trong" @(ViewBag.TinhTrangGhe == "con_trong" ? "selected" : "")>Còn ghế trống</option>
                                <option value="gan_het" @(ViewBag.TinhTrangGhe == "gan_het" ? "selected" : "")>Gần hết ghế (&gt;80%)</option>
                                <option value="het_cho" @(ViewBag.TinhTrangGhe == "het_cho" ? "selected" : "")>Hết chỗ</option>
                            </select>
                        </div>
                    </div>

                    <div class="row g-2 mt-3">
                        <div class="col-8">
                            <button type="submit" class="btn btn-admin-primary w-100">
                                <i class="bi bi-search me-2"></i>Lọc dữ liệu
                            </button>
                        </div>
                        <div class="col-4">
                            <a asp-action="Index" class="btn btn-outline-secondary w-100">
                                <i class="bi bi-arrow-clockwise"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Biểu đồ thống kê -->
    <div class="row mb-4">
        <div class="col-lg-6">
            <div class="card admin-card">
                <div class="card-header bg-white py-3">
                    <h5 class="mb-0"><i class="bi bi-pie-chart me-2"></i>Phân bố trạng thái vé</h5>
                </div>
                <div class="card-body">
                    <canvas id="ticketStatusChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>
        <div class="col-lg-6">
            <div class="card admin-card">
                <div class="card-header bg-white py-3">
                    <h5 class="mb-0"><i class="bi bi-bar-chart me-2"></i>Tỷ lệ lấp đầy theo chuyến xe</h5>
                </div>
                <div class="card-body">
                    <canvas id="occupancyChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Thống kê nhanh theo tuyến đường -->
    <div class="card admin-card mb-4">
        <div class="card-header bg-white py-3">
            <h5 class="mb-0"><i class="bi bi-graph-up me-2"></i>Top tuyến đường theo số vé</h5>
        </div>
        <div class="card-body">
            <div class="row">
                @{
                    var routeStats = Model.GroupBy(c => new { c.DiemDiDisplay, c.DiemDenDisplay })
                                          .Select(g => new {
                                              Route = $"{g.Key.DiemDiDisplay} → {g.Key.DiemDenDisplay}",
                                              TotalTickets = g.Sum(c => c.Ves?.Count ?? 0),
                                              TotalRevenue = g.Sum(c => c.Ves?.Sum(v => v.GiaVe) ?? 0),
                                              TripCount = g.Count()
                                          })
                                          .OrderByDescending(r => r.TotalTickets)
                                          .Take(5)
                                          .ToList();
                }
                @foreach (var route in routeStats)
                {
                    <div class="col-md-6 col-lg-4 mb-3">
                        <div class="card border-0 bg-light h-100">
                            <div class="card-body">
                                <h6 class="card-title text-primary">@route.Route</h6>
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <div class="text-muted small">Số vé</div>
                                        <div class="fw-bold">@route.TotalTickets</div>
                                    </div>
                                    <div>
                                        <div class="text-muted small">Chuyến xe</div>
                                        <div class="fw-bold">@route.TripCount</div>
                                    </div>
                                    <div>
                                        <div class="text-muted small">Doanh thu</div>
                                        <div class="fw-bold text-success">@route.TotalRevenue.ToString("#,##0")₫</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                }
                @if (!routeStats.Any())
                {
                    <div class="col-12">
                        <div class="text-center text-muted py-3">
                            <i class="bi bi-info-circle me-2"></i>Chưa có dữ liệu thống kê
                        </div>
                    </div>
                }
            </div>
        </div>
    </div>

    <!-- Danh sách chuyến xe -->
    <div class="card admin-card">
        <div class="card-header bg-white py-3 d-flex justify-content-between align-items-center">
            <h5 class="mb-0"><i class="bi bi-list-ul me-2"></i>Danh sách chuyến xe (@Model.Count)</h5>
            <div class="d-flex gap-2">
                @if (Model.Count > 0)
                {
                    <div class="dropdown">
                        <button class="btn btn-sm btn-outline-success dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="bi bi-file-earmark-excel me-1"></i>Xuất báo cáo
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" onclick="exportReport('summary')">
                                <i class="bi bi-graph-up me-2"></i>Báo cáo tổng hợp
                            </a></li>
                            <li><a class="dropdown-item" href="#" onclick="exportReport('detailed')">
                                <i class="bi bi-table me-2"></i>Báo cáo chi tiết
                            </a></li>
                            <li><a class="dropdown-item" href="#" onclick="exportReport('revenue')">
                                <i class="bi bi-cash-coin me-2"></i>Báo cáo doanh thu
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" onclick="exportReport('csv')">
                                <i class="bi bi-filetype-csv me-2"></i>Xuất CSV
                            </a></li>
                        </ul>
                    </div>
                }
                <button type="button" class="btn btn-sm btn-outline-primary" id="btnRefresh">
                    <i class="bi bi-arrow-clockwise me-1"></i>Làm mới
                </button>
            </div>
        </div>
        <div class="card-body p-0">
            @if (Model.Count > 0)
            {
                <div class="table-responsive">
                    <table class="table table-hover align-middle admin-table mb-0">
                        <thead>
                            <tr>
                                <th>Tuyến đường</th>
                                <th>Thời gian</th>
                                <th>Xe & Tài xế</th>
                                <th>Trạng thái ghế</th>
                                <th>Thống kê vé</th>
                                <th width="120px" class="text-center">Thao tác</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var chuyenXe in Model.OrderBy(c => c.NgayKhoiHanh))
                            {
                                var totalSeats = chuyenXe.Xe?.SoGhe ?? 0;
                                var bookedSeats = chuyenXe.Ves?.Count ?? 0;
                                var percentBooked = totalSeats > 0 ? (int)((double)bookedSeats / totalSeats * 100) : 0;
                                var isUpcoming = chuyenXe.NgayKhoiHanh > DateTime.Now;
                                var statusClass = isUpcoming ? "bg-success" : "bg-secondary";
                                var statusText = isUpcoming ? "Sắp khởi hành" : "Đã khởi hành";
                                
                                if (chuyenXe.TrangThai == false)
                                {
                                    statusClass = "bg-danger";
                                    statusText = "Đã hủy";
                                }
                                
                                var bookedTickets = chuyenXe.Ves?.Count(v => v.TrangThai == TrangThaiVe.DaDat || v.TrangThai == TrangThaiVe.DaThanhToan) ?? 0;
                                var usedTickets = chuyenXe.Ves?.Count(v => v.TrangThai == TrangThaiVe.DaSuDung) ?? 0;
                                var completedTickets = chuyenXe.Ves?.Count(v => v.TrangThai == TrangThaiVe.DaHoanThanh) ?? 0;
                                var cancelledTickets = chuyenXe.Ves?.Count(v => v.TrangThai == TrangThaiVe.DaHuy) ?? 0;

                                <tr>
                                    <td>
                                        <div class="fw-semibold">@chuyenXe.DiemDiDisplay → @chuyenXe.DiemDenDisplay</div>
                                        <div class="text-muted small">@chuyenXe.TuyenDuong?.KhoangCach km</div>
                                        <div><span class="badge @statusClass">@statusText</span></div>
                                    </td>
                                    <td>
                                        <div class="fw-medium">@chuyenXe.NgayKhoiHanh.ToString("dd/MM/yyyy")</div>
                                        <div class="text-muted small">@chuyenXe.NgayKhoiHanh.ToString("HH:mm")</div>
                                        @{
                                            var daysUntil = (chuyenXe.NgayKhoiHanh.Date - DateTime.Now.Date).TotalDays;
                                            if (daysUntil >= 0 && daysUntil <= 3 && isUpcoming)
                                            {
                                                <div class="text-danger small">
                                                    <i class="bi bi-exclamation-circle"></i>
                                                    @(daysUntil == 0 ? "Hôm nay" : $"Còn {daysUntil} ngày")
                                                </div>
                                            }
                                        }
                                    </td>
                                    <td>
                                        <div class="fw-medium">@chuyenXe.Xe?.BienSo</div>
                                        <div class="text-muted small">@chuyenXe.Xe?.LoaiXe - @chuyenXe.Xe?.SoGhe chỗ</div>
                                        <div class="text-muted small">
                                            @if (chuyenXe.TaiXe != null)
                                            {
                                                <i class="bi bi-person me-1"></i>@chuyenXe.TaiXe.HoTen
                                            }
                                            else
                                            {
                                                <span class="text-warning"><i class="bi bi-exclamation-triangle me-1"></i>Chưa phân công</span>
                                            }
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="flex-grow-1 me-2">
                                                <div class="progress" style="height: 10px;">
                                                    <div class="progress-bar @(percentBooked > 80 ? "bg-danger" : percentBooked > 50 ? "bg-warning" : "bg-success")" 
                                                         role="progressbar" 
                                                         style="width: @percentBooked%" 
                                                         aria-valuenow="@percentBooked" 
                                                         aria-valuemin="0" 
                                                         aria-valuemax="100">
                                                    </div>
                                                </div>
                                                <div class="text-muted small mt-1">@bookedSeats/@totalSeats chỗ (@percentBooked%)</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex flex-wrap gap-2">
                                            @if (bookedTickets > 0)
                                            {
                                                <span class="badge admin-badge bg-primary">@bookedTickets đặt</span>
                                            }
                                            @if (usedTickets > 0)
                                            {
                                                <span class="badge admin-badge bg-success">@usedTickets đón</span>
                                            }
                                            @if (completedTickets > 0)
                                            {
                                                <span class="badge admin-badge bg-info">@completedTickets hoàn thành</span>
                                            }
                                            @if (cancelledTickets > 0)
                                            {
                                                <span class="badge admin-badge bg-danger">@cancelledTickets hủy</span>
                                            }
                                            @if (bookedTickets == 0 && usedTickets == 0 && completedTickets == 0 && cancelledTickets == 0)
                                            {
                                                <span class="badge admin-badge bg-secondary">Chưa có vé</span>
                                            }
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex gap-2 justify-content-center">
                                            <a asp-action="Details" asp-route-id="@chuyenXe.ChuyenXeId" class="btn btn-sm btn-outline-primary" title="Xem chi tiết">
                                                <i class="bi bi-eye"></i>
                                            </a>
                                            @if (bookedSeats > 0)
                                            {
                                                <a asp-action="ExportPassengerList" asp-route-id="@chuyenXe.ChuyenXeId" class="btn btn-sm btn-outline-success" title="Xuất DS hành khách">
                                                    <i class="bi bi-file-earmark-excel"></i>
                                                </a>
                                            }
                                        </div>
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
            }
            else
            {
                <div class="text-center py-5">
                    <i class="bi bi-bus-front display-1 text-muted"></i>
                    <p class="mt-3 text-muted">Không tìm thấy chuyến xe nào trong khoảng thời gian đã chọn</p>
                    <a asp-action="Index" class="btn btn-outline-primary mt-2">Xem tất cả chuyến xe</a>
                </div>
            }
        </div>
    </div>
</div>

@section Scripts {
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        $(document).ready(function() {
            // Highlight menu item
            $('#menu-booking-management').addClass('active');

            // Làm mới trang
            $('#btnRefresh').click(function() {
                location.reload();
            });

            // Toggle bộ lọc nâng cao
            $('#toggleAdvancedFilter').click(function() {
                var $advancedFilters = $('#advancedFilters');
                var $icon = $(this).find('i');

                if ($advancedFilters.is(':visible')) {
                    $advancedFilters.slideUp();
                    $icon.removeClass('bi-chevron-up').addClass('bi-chevron-down');
                    $(this).find('.btn-text').text('Mở rộng');
                } else {
                    $advancedFilters.slideDown();
                    $icon.removeClass('bi-chevron-down').addClass('bi-chevron-up');
                    $(this).find('.btn-text').text('Thu gọn');
                }
            });

            // Kiểm tra nếu có tham số lọc nâng cao thì mở sẵn
            var hasAdvancedParams = '@ViewBag.TuNgay' || '@ViewBag.DenNgay' || '@ViewBag.LoaiXe' ||
                                   '@ViewBag.MinGia' || '@ViewBag.MaxGia' || '@ViewBag.TinhTrangGhe';
            if (hasAdvancedParams) {
                $('#advancedFilters').show();
                $('#toggleAdvancedFilter i').removeClass('bi-chevron-down').addClass('bi-chevron-up');
            }

            // Auto-submit form khi thay đổi ngày
            $('input[name="ngayKhoiHanh"]').change(function() {
                if ($(this).val()) {
                    $(this).closest('form').submit();
                }
            });

            // Validation cho khoảng giá
            $('input[name="minGia"], input[name="maxGia"]').on('input', function() {
                var minGia = parseInt($('input[name="minGia"]').val()) || 0;
                var maxGia = parseInt($('input[name="maxGia"]').val()) || 0;

                if (minGia > 0 && maxGia > 0 && minGia > maxGia) {
                    $(this).addClass('is-invalid');
                    if (!$(this).next('.invalid-feedback').length) {
                        $(this).after('<div class="invalid-feedback">Giá từ không thể lớn hơn giá đến</div>');
                    }
                } else {
                    $(this).removeClass('is-invalid');
                    $(this).next('.invalid-feedback').remove();
                }
            });

            // Validation cho khoảng ngày
            $('input[name="tuNgay"], input[name="denNgay"]').change(function() {
                var tuNgay = new Date($('input[name="tuNgay"]').val());
                var denNgay = new Date($('input[name="denNgay"]').val());

                if (tuNgay && denNgay && tuNgay > denNgay) {
                    $(this).addClass('is-invalid');
                    if (!$(this).next('.invalid-feedback').length) {
                        $(this).after('<div class="invalid-feedback">Từ ngày không thể lớn hơn đến ngày</div>');
                    }
                } else {
                    $('input[name="tuNgay"], input[name="denNgay"]').removeClass('is-invalid');
                    $('input[name="tuNgay"], input[name="denNgay"]').next('.invalid-feedback').remove();
                }
            });

            // Thêm tooltip cho các nút
            $('[data-bs-toggle="tooltip"]').tooltip();

            // Tạo biểu đồ thống kê
            createCharts();
        });

        // Hàm tạo biểu đồ
        function createCharts() {
            // Dữ liệu cho biểu đồ trạng thái vé
            var ticketData = {
                booked: @Model.Sum(c => c.Ves?.Count(v => v.TrangThai == TrangThaiVe.DaDat || v.TrangThai == TrangThaiVe.DaThanhToan) ?? 0),
                used: @Model.Sum(c => c.Ves?.Count(v => v.TrangThai == TrangThaiVe.DaSuDung) ?? 0),
                completed: @Model.Sum(c => c.Ves?.Count(v => v.TrangThai == TrangThaiVe.DaHoanThanh) ?? 0),
                cancelled: @Model.Sum(c => c.Ves?.Count(v => v.TrangThai == TrangThaiVe.DaHuy) ?? 0)
            };

            // Biểu đồ tròn trạng thái vé
            var ctx1 = document.getElementById('ticketStatusChart').getContext('2d');
            new Chart(ctx1, {
                type: 'doughnut',
                data: {
                    labels: ['Đã đặt/Thanh toán', 'Đã sử dụng', 'Đã hoàn thành', 'Đã hủy'],
                    datasets: [{
                        data: [ticketData.booked, ticketData.used, ticketData.completed, ticketData.cancelled],
                        backgroundColor: [
                            '#0d6efd',
                            '#198754',
                            '#17a2b8',
                            '#dc3545'
                        ],
                        borderWidth: 2,
                        borderColor: '#fff'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                padding: 20,
                                usePointStyle: true
                            }
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    var total = context.dataset.data.reduce((a, b) => a + b, 0);
                                    var percentage = ((context.parsed * 100) / total).toFixed(1);
                                    return context.label + ': ' + context.parsed + ' vé (' + percentage + '%)';
                                }
                            }
                        }
                    }
                }
            });

            // Dữ liệu cho biểu đồ tỷ lệ lấp đầy
            var occupancyData = [];
            var occupancyLabels = [];

            @foreach (var chuyenXe in Model.Take(10))
            {
                var totalSeats = chuyenXe.Xe?.SoGhe ?? 0;
                var bookedSeats = chuyenXe.Ves?.Count ?? 0;
                var percentage = totalSeats > 0 ? Math.Round((double)bookedSeats / totalSeats * 100, 1) : 0;

                <text>
                occupancyData.push(@percentage);
                occupancyLabels.push('@($"{chuyenXe.DiemDiDisplay} → {chuyenXe.DiemDenDisplay}")');
                </text>
            }

            // Biểu đồ cột tỷ lệ lấp đầy
            var ctx2 = document.getElementById('occupancyChart').getContext('2d');
            new Chart(ctx2, {
                type: 'bar',
                data: {
                    labels: occupancyLabels,
                    datasets: [{
                        label: 'Tỷ lệ lấp đầy (%)',
                        data: occupancyData,
                        backgroundColor: function(context) {
                            var value = context.parsed.y;
                            if (value >= 80) return '#dc3545';
                            if (value >= 50) return '#ffc107';
                            return '#198754';
                        },
                        borderColor: function(context) {
                            var value = context.parsed.y;
                            if (value >= 80) return '#dc3545';
                            if (value >= 50) return '#ffc107';
                            return '#198754';
                        },
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100,
                            ticks: {
                                callback: function(value) {
                                    return value + '%';
                                }
                            }
                        },
                        x: {
                            ticks: {
                                maxRotation: 45,
                                minRotation: 45
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    return 'Tỷ lệ lấp đầy: ' + context.parsed.y + '%';
                                }
                            }
                        }
                    }
                }
            });
        }

        // Hàm xuất báo cáo
        function exportReport(type) {
            // Lấy các tham số lọc hiện tại
            var params = new URLSearchParams();

            var ngayKhoiHanh = $('input[name="ngayKhoiHanh"]').val();
            var trangThai = $('select[name="trangThai"]').val();
            var searchKeyword = $('input[name="searchKeyword"]').val();
            var tuNgay = $('input[name="tuNgay"]').val();
            var denNgay = $('input[name="denNgay"]').val();
            var loaiXe = $('select[name="loaiXe"]').val();
            var minGia = $('input[name="minGia"]').val();
            var maxGia = $('input[name="maxGia"]').val();
            var tinhTrangGhe = $('select[name="tinhTrangGhe"]').val();

            if (ngayKhoiHanh) params.append('ngayKhoiHanh', ngayKhoiHanh);
            if (trangThai && trangThai !== 'all') params.append('trangThai', trangThai);
            if (searchKeyword) params.append('searchKeyword', searchKeyword);
            if (tuNgay) params.append('tuNgay', tuNgay);
            if (denNgay) params.append('denNgay', denNgay);
            if (loaiXe) params.append('loaiXe', loaiXe);
            if (minGia) params.append('minGia', minGia);
            if (maxGia) params.append('maxGia', maxGia);
            if (tinhTrangGhe) params.append('tinhTrangGhe', tinhTrangGhe);

            params.append('reportType', type);

            // Mở URL xuất báo cáo trong tab mới
            var url = '@Url.Action("ExportReport", "BookingManagement")?' + params.toString();
            window.open(url, '_blank');
        }
    </script>
}

@section Styles {
    <style>
        /* Cải thiện giao diện card */
        .admin-card {
            border: none;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            transition: all 0.3s ease;
        }

        .admin-card:hover {
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
            transform: translateY(-2px);
        }

        /* Cải thiện bộ lọc */
        #advancedFilters {
            border-top: 1px solid #dee2e6;
            padding-top: 1rem;
            margin-top: 1rem;
        }

        /* Cải thiện biểu đồ */
        .card-body canvas {
            max-height: 300px;
        }

        /* Cải thiện responsive */
        @media (max-width: 768px) {
            .card-body canvas {
                max-height: 250px;
            }

            .table-responsive {
                font-size: 0.875rem;
            }

            .btn-group-sm > .btn, .btn-sm {
                padding: 0.25rem 0.5rem;
                font-size: 0.75rem;
            }
        }

        /* Cải thiện progress bar */
        .progress {
            background-color: #e9ecef;
            border-radius: 0.375rem;
        }

        .progress-bar {
            transition: width 0.6s ease;
        }

        /* Cải thiện badge */
        .admin-badge {
            font-size: 0.75rem;
            padding: 0.35em 0.65em;
            border-radius: 0.375rem;
        }

        /* Cải thiện icon box */
        .icon-box {
            width: 3rem;
            height: 3rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* Cải thiện dropdown */
        .dropdown-menu {
            border: none;
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
            border-radius: 0.5rem;
        }

        .dropdown-item {
            padding: 0.5rem 1rem;
            transition: all 0.2s ease;
        }

        .dropdown-item:hover {
            background-color: #f8f9fa;
            transform: translateX(5px);
        }

        /* Animation cho loading */
        .loading {
            opacity: 0.6;
            pointer-events: none;
        }

        .loading::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 20px;
            height: 20px;
            margin: -10px 0 0 -10px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Cải thiện form validation */
        .is-invalid {
            border-color: #dc3545;
            box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
        }

        .invalid-feedback {
            display: block;
            width: 100%;
            margin-top: 0.25rem;
            font-size: 0.875em;
            color: #dc3545;
        }

        /* Cải thiện table */
        .admin-table th {
            background-color: #f8f9fa;
            border-bottom: 2px solid #dee2e6;
            font-weight: 600;
            color: #495057;
        }

        .admin-table td {
            vertical-align: middle;
            border-bottom: 1px solid #dee2e6;
        }

        .admin-table tbody tr:hover {
            background-color: #f8f9fa;
        }

        /* Cải thiện breadcrumb */
        .breadcrumb {
            background-color: transparent;
            padding: 0;
            margin-bottom: 1rem;
        }

        .breadcrumb-item + .breadcrumb-item::before {
            content: "›";
            color: #6c757d;
        }

        /* Dark mode support */
        @media (prefers-color-scheme: dark) {
            .admin-card {
                background-color: #2d3748;
                color: #e2e8f0;
            }

            .admin-table th {
                background-color: #4a5568;
                color: #e2e8f0;
            }
        }
    </style>
}
